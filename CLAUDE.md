# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Essential Commands
- **Run the app**: `flutter run`
- **Hot reload**: `r` (in terminal during flutter run)
- **Hot restart**: `R` (in terminal during flutter run)
- **Build for production**: `flutter build apk` (Android) or `flutter build ios` (iOS)
- **Clean build**: `flutter clean && flutter pub get`
- **Get dependencies**: `flutter pub get`

### Code Quality
- **Analyze code**: `flutter analyze`
- **Run tests**: `flutter test`
- **Format code**: `dart format lib/`

### Device/Platform Specific
- **List devices**: `flutter devices`
- **Run on specific device**: `flutter run -d <device-id>`
- **Build web**: `flutter build web`

## Architecture Overview

This is a Flutter application using **GetX** framework for state management, navigation, and dependency injection. The app is a chat-centric AI bot interface with multi-bot support, data visualization, and task scheduling capabilities.

### Core Architecture Pattern

The app follows a **layered architecture** with clear separation of concerns:

```
┌─────────────────┐
│   Presentation  │ <- Pages, Widgets (UI Layer)
├─────────────────┤
│   Controllers   │ <- Business Logic, User Interactions  
├─────────────────┤
│    Services     │ <- API Calls, Business Rules
├─────────────────┤
│   Providers     │ <- State Management, Data Persistence
└─────────────────┘
```

### State Management (GetX)

- **Providers**: Act as data repositories with reactive observables (`Rx` variables)
- **Controllers**: Handle UI logic and user interactions, inject dependencies
- **Services**: Encapsulate business logic, API communications, and external integrations
- **Service Locator**: Centralized dependency registration in `lib/services/service_locator.dart`

### Key Architectural Components

1. **Reactive State Management**: Uses GetX observables (`RxList`, `RxBool`, etc.) for automatic UI updates
2. **Dependency Injection**: Services and providers registered globally via GetX
3. **Navigation**: Declarative routing with GetX navigation system
4. **Data Flow**: Unidirectional data flow from Services → Providers → Controllers → UI

## Application Features

### Core Functionality
- **Multi-Bot Chat**: Support for multiple AI bots with individual chat histories
- **Authentication**: User login with token-based authentication and multi-tenancy
- **Real-time Messaging**: Chat interface with optimistic UI updates and loading states
- **AI Analysis**: Intelligent analysis of bot messages with categorization
- **Data Visualization**: Multiple chart types (line, bar, heatmap) for data analysis
- **Task Scheduling**: Cron-based message scheduling with recurring tasks
- **Favorites System**: Save and organize frequently used messages
- **Export Functionality**: CSV export and share capabilities for data

### Key Technical Features
- **Temporary Messages**: Immediate UI feedback while API calls are in progress
- **Error Handling**: Comprehensive error states and user feedback
- **Offline Support**: SharedPreferences for local data persistence
- **Cross-Platform**: Android, iOS, Web, Windows, and macOS support

## Code Organization

### Directory Structure
- `lib/constants/` - API endpoints and app constants
- `lib/controllers/` - GetX controllers for UI logic
- `lib/models/` - Data models and DTOs
- `lib/pages/` - Screen components and UI pages
- `lib/providers/` - State management and data repositories
- `lib/services/` - Business logic and API integration
- `lib/utils/` - Helper functions and utilities
- `lib/widgets/` - Reusable UI components organized by feature

### Key Patterns

1. **Provider Pattern**: Data repositories with reactive state
2. **Service Layer**: Abstracted business logic and API calls
3. **Command Pattern**: Clear separation between user actions and business logic
4. **Repository Pattern**: Providers act as data access layers

## Important Implementation Details

### State Management Flow
1. User interaction triggers Controller method
2. Controller calls appropriate Service method
3. Service performs business logic/API call
4. Service updates Provider state via reactive variables
5. UI automatically rebuilds based on Provider state changes

### Chat Architecture
- Each bot has independent chat history managed by `HistoryProvider`
- Messages support temporary states for optimistic UI updates
- Real-time suggestions system based on message analysis
- AI analysis integration for intelligent message categorization

### Authentication Flow
- Token-based authentication with automatic refresh
- Multi-tenant support with user context management
- Secure storage via SharedPreferences
- Automatic logout on token expiration

### Data Visualization
- Dynamic chart generation based on message content
- Support for line charts, bar charts, and heatmaps
- CSV export functionality for data analysis
- Responsive design for different screen sizes

### Task Scheduling
- Cron expression parsing for flexible scheduling
- Background task management with persistent storage
- User-friendly scheduling interface with validation
- Task execution tracking and error handling

## Development Guidelines

### Adding New Features
1. Create model in `lib/models/` if new data structure needed
2. Add service in `lib/services/` for business logic
3. Update provider in `lib/providers/` for state management
4. Create controller in `lib/controllers/` for UI logic
5. Implement UI in `lib/pages/` and `lib/widgets/`
6. Register new services in `ServiceLocator.init()`

### API Integration
- All API endpoints defined in `lib/constants/api_endpoints.dart`
- Use Dio HTTP client configured in services
- Implement proper error handling and loading states
- Follow existing patterns for authentication headers

### State Updates
- Use reactive variables (`RxList`, `RxBool`, etc.) in providers
- Call `.update()` or modify `.value` to trigger UI rebuilds
- Handle loading and error states consistently across the app
- Implement optimistic updates where appropriate

### UI Development
- Follow Material Design 3 principles
- Use GetX navigation for route management
- Implement responsive design for multiple screen sizes
- Maintain consistent theming with light/dark mode support