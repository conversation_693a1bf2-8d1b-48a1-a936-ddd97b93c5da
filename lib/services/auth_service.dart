import 'package:dio/dio.dart';
import 'package:get/get.dart' hide FormData, MultipartFile, Response;
import '../models/user_models.dart';
import '../constants/api_endpoints.dart';
import '../providers/user_provider.dart';
import 'dart:convert';

class AuthService {
  late final Dio _dio;
  final UserProvider _userProvider = Get.find<UserProvider>();

  AuthService() {
    _dio = Dio();
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 30);
  }

  /// Intenta hacer login automático si hay tokens válidos guardados
  Future<bool> tryAutoLogin() async {
    try {
      // Cargar datos guardados
      await _userProvider.loadUserData();
      
      // Verificar si hay datos de login guardados
      if (_userProvider.loginData == null || 
          _userProvider.baseUrl.isEmpty) {
        return false;
      }
      
      // Verificar si el token ha expirado
      if (_userProvider.isTokenExpired) {
        // Intentar refrescar el token
        return await _refreshToken();
      }
      
      // Si el token va a expirar pronto, intentar refrescarlo
      if (_userProvider.willTokenExpireSoon) {
        await _refreshToken();
      }
      
      return true;
    } catch (e) {
      // Si hay error, limpiar datos y retornar false
      _userProvider.logout();
      return false;
    }
  }

  /// Refresca el access token usando el refresh token
  Future<bool> _refreshToken() async {
    try {
      if (_userProvider.refreshToken.isEmpty || 
          _userProvider.baseUrl.isEmpty) {
        return false;
      }

      final url = '${_userProvider.baseUrl}${ApiEndpoints.refreshTokenEndpoint}';
      
      final response = await _dio.post(
        url,
        data: jsonEncode({
          'refresh_token': _userProvider.refreshToken,
        }),
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ${_userProvider.refreshToken}',
          },
        ),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        final newAccessToken = data['access_token'] as String;
        final newRefreshToken = data['refresh_token'] as String?
            ?? _userProvider.refreshToken;
        final ttl = data['ttl'] as int;
        
        // Actualizar tokens en el provider
        _userProvider.updateTokens(newAccessToken, newRefreshToken, ttl);
        
        return true;
      } else {
        _userProvider.logout();
        return false;
      }
    } catch (e) {
      _userProvider.logout();
      return false;
    }
  }

  /// Realiza el login con usuario y contraseña
  Future<LoginResponse> login({
    required String baseUrl,
    required String username,
    required String password,
  }) async {
    try {
      // Preparar los datos del formulario
      final formData = FormData.fromMap({
        'user': username,
        'password': password,
        'grant_type': 'password',
      });

      // Construir la URL completa
      final url = '$baseUrl${ApiEndpoints.loginEndpoint}';

      // Realizar la petición
      final response = await _dio.post(
        url,
        data: formData,
        options: Options(headers: {'Content-Type': 'multipart/form-data'}),
      );

      // Verificar el código de estado
      if (response.statusCode == 200) {
        // Parsear la respuesta
        final loginResponse = LoginResponse.fromJson(response.data);

        // Guardar datos en el provider
        _userProvider.setLoginData(loginResponse, baseUrl);

        return loginResponse;
      } else {
        throw AuthException(
          'Error en el servidor: ${response.statusCode}',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      // Manejar errores específicos de Dio
      throw _handleDioError(e);
    } catch (e) {
      // Manejar otros errores
      throw AuthException('Error inesperado: $e');
    }
  }

  /// Maneja los errores de Dio y los convierte en AuthException
  AuthException _handleDioError(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
        return AuthException('Tiempo de conexión agotado');
      case DioExceptionType.receiveTimeout:
        return AuthException('Tiempo de respuesta agotado');
      case DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode;
        // Preferir el campo 'detail' si existe, para mensajes más amigables
        final data = e.response?.data;
        String message = 'Error del servidor';
        if (data != null) {
          if (data['detail'] != null &&
              (statusCode == 401 || statusCode == 403)) {
            message = data['detail'];
          } else if (data['message'] != null) {
            message = data['message'];
          }
        }
        return AuthException(
          'Error $statusCode: $message',
          statusCode: statusCode,
        );
      case DioExceptionType.cancel:
        return AuthException('Petición cancelada');
      case DioExceptionType.connectionError:
        return AuthException(
          'Error de conexión. Verifique su conexión a internet',
        );
      case DioExceptionType.unknown:
        return AuthException('Error desconocido: ${e.message}');
      default:
        return AuthException('Error de red: ${e.message}');
    }
  }

  /// Verifica si el token actual es válido haciendo una petición autenticada
  Future<bool> validateCurrentToken() async {
    try {
      if (_userProvider.accessToken.isEmpty || 
          _userProvider.baseUrl.isEmpty) {
        return false;
      }

      // Usar el endpoint de versión para validar el token
      final url = '${_userProvider.baseUrl}${ApiEndpoints.versionEndpoint}';
      
      final response = await _dio.get(
        url,
        options: Options(
          headers: {
            'Authorization': 'Bearer ${_userProvider.accessToken}',
          },
        ),
      );

      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }

  /// Valida la conexión con el servidor usando el endpoint de versión
  Future<bool> validateConnection(String baseUrl) async {
    try {
      // Construir la URL completa para el endpoint de versión
      final url = '$baseUrl${ApiEndpoints.versionEndpoint}';

      // Realizar la petición GET al endpoint de versión
      final response = await _dio.get(url);

      // Retornar true si el código de estado es 200
      return response.statusCode == 200;
    } catch (e) {
      // Si hay cualquier error, retornar false
      return false;
    }
  }

  /// Ejecuta logout y limpia todos los datos
  Future<void> logout() async {
    _userProvider.logout();
  }

  /// Cierra el cliente Dio
  void dispose() {
    _dio.close();
  }
}

/// Excepción personalizada para errores de autenticación
class AuthException implements Exception {
  final String message;
  final int? statusCode;

  AuthException(this.message, {this.statusCode});

  @override
  String toString() => 'AuthException: $message';
}
