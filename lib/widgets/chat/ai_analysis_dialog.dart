import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../services/chat_service.dart';
import '../../models/history_model.dart';
import '../../providers/history_provider.dart';
import '../../providers/user_provider.dart';

class AiAnalysisDialog extends StatefulWidget {
  final ChatHistory message;
  final String botCategory;

  const AiAnalysisDialog({
    super.key,
    required this.message,
    required this.botCategory,
  });

  @override
  State<AiAnalysisDialog> createState() => _AiAnalysisDialogState();
}

class _AiAnalysisDialogState extends State<AiAnalysisDialog> {
  final TextEditingController _promptController = TextEditingController();
  final ChatService _chatService = Get.find<ChatService>();
  final HistoryProvider _historyProvider = Get.find<HistoryProvider>();
  final UserProvider _userProvider = Get.find<UserProvider>();
  bool _isLoading = false;
  final String _selectedProvider = 'Google';

  final List<String> _promptSuggestions = [
    'Analiza los datos y proporciona insights clave',
    'Identifica patrones y tendencias en la información',
    'Sugiere acciones basadas en estos resultados',
  ];

  @override
  void dispose() {
    _promptController.dispose();
    super.dispose();
  }

  Future<void> _performAnalysis() async {
    if (_promptController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Por favor ingresa un prompt para el análisis'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final result = await _chatService.performAiAnalysis(
        originalQuestion: widget.message.mensaje,
        responseData: widget.message.respuesta.toString(),
        analysisPrompt: _promptController.text.trim(),
        botCategory: widget.botCategory,
        provider: _selectedProvider,
      );

      final analysisText =
          result['analysis'] ?? 'No se pudo obtener el análisis';

      // Agregar el análisis como un mensaje temporal del bot
      final currentBotId = _historyProvider.currentBotId;
      final currentUserId = _userProvider.currentUser?.usuario ?? 'unknown';

      _historyProvider.addTemporaryBotMessage(
        currentBotId,
        analysisText,
        currentUserId,
      );

      setState(() {
        _isLoading = false;
      });

      // Cerrar el diálogo después de agregar el mensaje
      if (mounted) {
        Navigator.of(context).pop();

        // Mostrar un mensaje de confirmación
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Análisis IA agregado al chat'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error al realizar análisis: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                const Icon(Icons.psychology, color: Colors.blue),
                const SizedBox(width: 8),
                const Text(
                  'Análisis IA',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const Divider(),

            // Scrollable content
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Prompt suggestions
                    const Text(
                      'Sugerencias de prompts:',
                      style: TextStyle(fontWeight: FontWeight.w500),
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      runSpacing: 4,
                      children: _promptSuggestions.map((suggestion) {
                        return ActionChip(
                          label: Text(
                            suggestion,
                            style: const TextStyle(fontSize: 12),
                          ),
                          onPressed: () {
                            _promptController.text = suggestion;
                          },
                        );
                      }).toList(),
                    ),
                    const SizedBox(height: 16),

                    // Prompt input
                    TextField(
                      controller: _promptController,
                      maxLines: 4,
                      decoration: const InputDecoration(
                        labelText: 'Prompt para análisis',
                        hintText:
                            'Describe qué tipo de análisis quieres realizar...',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Analyze button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _performAnalysis,
                        child: _isLoading
                            ? const Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SizedBox(
                                    width: 16,
                                    height: 16,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                    ),
                                  ),
                                  SizedBox(width: 8),
                                  Text('Analizando...'),
                                ],
                              )
                            : const Text('Realizar Análisis'),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Información sobre el resultado
                    if (!_isLoading) ...[
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.blue.shade50,
                          border: Border.all(color: Colors.blue.shade200),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Row(
                          children: [
                            Icon(
                              Icons.info_outline,
                              color: Colors.blue,
                              size: 20,
                            ),
                            SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                'El resultado del análisis aparecerá como un mensaje temporal del bot en el chat.',
                                style: TextStyle(
                                  color: Colors.blue,
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
