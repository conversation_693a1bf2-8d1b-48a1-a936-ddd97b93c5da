import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../models/history_model.dart';
import '../../controllers/chat_controller.dart';
import '../../utils/date_utils.dart' as app_date;
import 'message_table_widget.dart';
import 'user_message_options_sheet.dart';
import 'bot_message_options_sheet.dart';
import 'suggestions_chips.dart';

class MessageBubble extends StatelessWidget {
  final ChatHistory message;
  final bool isUserMessage;
  final String botId;
  final bool isLastMessage;

  const MessageBubble({
    super.key,
    required this.message,
    required this.isUserMessage,
    required this.botId,
    this.isLastMessage = false,
  });

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<ChatController>(tag: botId);

    return Column(
      children: [
        // Mensaje del usuario (siempre se muestra)
        _buildUserMessage(context, controller),

        // Respuesta del bot (si existe)
        if (message.respuesta != null &&
            message.respuesta.toString().isNotEmpty)
          _buildBotMessage(context, controller),

        // Chips de sugerencias (solo en el último mensaje del bot que tenga sugerencias)
        if (isLastMessage &&
            message.respuesta != null &&
            message.respuesta.toString().isNotEmpty &&
            message.suggestions != null &&
            message.suggestions!.isNotEmpty)
          SuggestionsChips(suggestions: message.suggestions!, botId: botId),
      ],
    );
  }

  Widget _buildUserMessage(BuildContext context, ChatController controller) {
    final theme = Theme.of(context);
    final isTemporaryMessage =
        message.rid < 0; // Mensajes temporales tienen RID negativo

    // Para mensajes temporales, no usar Obx ya que no tienen favoritos
    if (isTemporaryMessage) {
      return Align(
        alignment: Alignment.centerRight,
        child: Container(
          constraints: BoxConstraints(
            maxWidth: MediaQuery.of(context).size.width * 0.75,
          ),
          margin: const EdgeInsets.symmetric(vertical: 8),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
          decoration: BoxDecoration(
            color: theme.colorScheme.primary.withValues(alpha: 0.7),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            message.mensaje,
                            style: TextStyle(
                              color: theme.colorScheme.onPrimary,
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(left: 8),
                          child: SizedBox(
                            width: 12,
                            height: 12,
                            child: CircularProgressIndicator(
                              strokeWidth: 1.5,
                              color: theme.colorScheme.onPrimary.withValues(
                                alpha: 0.7,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              Padding(
                padding: const EdgeInsets.only(top: 4),
                child: Text(
                  app_date.AppDateUtils.formatDateTime(message.fechaRegistro),
                  style: TextStyle(
                    fontSize: 10,
                    color: theme.colorScheme.onPrimary.withValues(alpha: 0.7),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    // Para mensajes normales, usar Obx para favoritos
    return Obx(() {
      final bool isFavorite = controller.isFavorite(message.rid);

      return Align(
        alignment: Alignment.centerRight,
        child: Container(
          constraints: BoxConstraints(
            maxWidth: MediaQuery.of(context).size.width * 0.75,
          ),
          margin: const EdgeInsets.symmetric(vertical: 8),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
          decoration: BoxDecoration(
            color: theme.colorScheme.primary,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (isFavorite)
                    Padding(
                      padding: const EdgeInsets.only(right: 4),
                      child: Icon(
                        Icons.favorite,
                        size: 14,
                        color: Colors.red.withValues(alpha: 0.8),
                      ),
                    ),
                  Expanded(
                    child: Text(
                      message.mensaje,
                      style: TextStyle(color: theme.colorScheme.onPrimary),
                    ),
                  ),
                  InkWell(
                    onTap: () => _showUserMessageOptions(context),
                    child: Icon(
                      Icons.more_vert,
                      size: 18,
                      color: theme.colorScheme.onPrimary.withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ),
              Padding(
                padding: const EdgeInsets.only(top: 4),
                child: Text(
                  app_date.AppDateUtils.formatDateTime(message.fechaRegistro),
                  style: TextStyle(
                    fontSize: 10,
                    color: theme.colorScheme.onPrimary.withValues(alpha: 0.7),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildBotMessage(BuildContext context, ChatController controller) {
    final theme = Theme.of(context);

    return Align(
      alignment: Alignment.centerLeft,
      child: Container(
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.75,
        ),
        margin: const EdgeInsets.symmetric(vertical: 8),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        decoration: BoxDecoration(
          color: theme.colorScheme.secondaryContainer,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: message.mensajeTipo == 'TablaJSON'
                      ? MessageTableWidget(message: message)
                      : Text(
                          message.respuesta.toString(),
                          style: TextStyle(
                            color: theme.colorScheme.onSecondaryContainer,
                          ),
                        ),
                ),
                InkWell(
                  onTap: () => _showBotMessageOptions(context),
                  child: Icon(
                    Icons.more_vert,
                    size: 18,
                    color: theme.colorScheme.onSecondaryContainer.withValues(
                      alpha: 0.7,
                    ),
                  ),
                ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.only(top: 4),
              child: Text(
                app_date.AppDateUtils.formatDateTime(message.fechaRegistro),
                style: TextStyle(
                  fontSize: 10,
                  color: theme.colorScheme.onSecondaryContainer.withValues(
                    alpha: 0.7,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showUserMessageOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) =>
          UserMessageOptionsSheet(message: message, botId: botId),
    );
  }

  void _showBotMessageOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) =>
          BotMessageOptionsSheet(message: message, botId: botId),
    );
  }
}
