import 'package:flutter/material.dart';
import '../models/history_model.dart';
import '../widgets/chat/message_table_widget.dart';

class FullTablePage extends StatelessWidget {
  final ChatHistory message;

  const FullTablePage({super.key, required this.message});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Tabla Completa')),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: MessageTableWidget(message: message, showAllRows: true),
      ),
    );
  }
}
